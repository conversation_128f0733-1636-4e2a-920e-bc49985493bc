"""
Video Metadata Agent
-------------------
Generates video titles, descriptions, and hashtags based on story content and trending keywords.
"""

import os
import re
import json
import logging
from typing import List

from crewai import Task, Crew, Process

from utils.agent_factory import create_rate_limited_agent
from utils.parsers import GlobalMetadataParser, ShortSpecificMetadataParser

from inference.dataforseo_client import DataForSEOClient

from models.schema import Story, TrendingKeyword, GlobalMetadata, ShortSpecificMetadata, Short

logger = logging.getLogger(__name__)


class GlobalMetadataAgent:
    """
    Agent for generating global metadata that applies to all shorts in a story.

    This agent creates base titles and hashtags that will be used across all shorts
    with part numbering (e.g., "Part 1 | [base-title]", "Part 2 | [base-title]").
    """

    def __init__(self, verbose: bool = False, model: str = "gpt-4o-mini", provider: str = "openai"):
        """
        Initialize the GlobalMetadataAgent.

        Args:
            verbose (bool): Enable verbose output from CrewAI agents
            model (str): Model name to use for LLM
            provider (str): Provider to use for LLM
        """
        self.verbose = verbose
        self.model = model
        self.provider = provider

        # Initialize DataForSEO client for trending keywords
        self.dataforseo_client = DataForSEOClient()

    def generate_global_metadata(self, story: Story, keywords: List[str]) -> GlobalMetadata:
        """
        Generate global metadata for the complete story.

        Args:
            story (Story): Complete story object
            keywords (List[str]): Pre-generated keywords for the story

        Returns:
            GlobalMetadata: Global metadata including base title and base hashtags
        """
        logger.info(f"Generating global metadata for story: {story.title}")

        # Get trending data from DataForSEO
        trending_keywords = self._get_trending_keywords(keywords)

        # Generate global metadata using trending keywords
        global_metadata = self._generate_global_metadata(story, trending_keywords)

        logger.info("Global metadata generation completed successfully")
        return global_metadata

    def _get_trending_keywords(self, initial_keywords: List[str]) -> List[TrendingKeyword]:
        """
        Get trending keywords from DataForSEO based on initial keywords.

        Args:
            initial_keywords (List[str]): List of initial keywords

        Returns:
            List[TrendingKeyword]: List of trending keywords
        """
        try:
            keywords_to_check = initial_keywords
            trending_data = self.dataforseo_client.get_trending_keywords(keywords_to_check)

            if trending_data and len(trending_data) > 0:
                logger.info(f"Found {len(trending_data)} trending keywords")
                return trending_data

            else:
                logger.warning("No trending keywords found, using original keywords")
                # Convert initial keywords to TrendingKeyword objects
                return [TrendingKeyword(keyword=kw, average_trend=0.0, max_trend=0, data_points=0) for kw in keywords_to_check]

        except Exception as e:
            logger.warning(f"Error getting trending keywords: {str(e)}")
            # Fallback to original keywords
            return [TrendingKeyword(keyword=kw, average_trend=0.0, max_trend=0, data_points=0) for kw in initial_keywords]

    def _generate_global_metadata(self, story: Story, trending_keywords: List[TrendingKeyword]) -> GlobalMetadata:
        """
        Generate global metadata using trending keywords.

        Args:
            story (Story): Complete story object
            trending_keywords (List[TrendingKeyword]): List of trending keywords

        Returns:
            GlobalMetadata: Global metadata including base title and base hashtags
        """
        # Prepare story content
        story_content = f"Title: {story.title}\n\n"
        for scene in story.scenes:
            story_content += f"Scene {scene.scene_number}: {scene.narration}\n\n"

        # Prepare trending keywords text
        if trending_keywords:
            trending_keywords_text = "TRENDING KEYWORDS TO INCORPORATE:\n"
            for tk in trending_keywords:
                trending_keywords_text += f"- {tk.keyword} (Average Trend: {tk.average_trend}, Max Trend: {tk.max_trend})\n"
        else:
            trending_keywords_text = "No trending keywords available."

        # Create the global metadata generation agent
        global_metadata_agent = create_rate_limited_agent(
            role="Global Video Metadata Strategist",
            goal="Create compelling base titles and hashtags that will work across all shorts in a story series",
            backstory="""You are a master of viral content strategy and social media optimization.
            You specialize in creating base titles and hashtag sets that work effectively across
            multi-part video series. You understand how to create consistent branding while
            maximizing discoverability and engagement across all parts of a story series.""",
            model=self.model,
            provider=self.provider,
            verbose=self.verbose,
            allow_delegation=False,
        )

        # Create parser for structured output
        parser = GlobalMetadataParser.create()
        format_instructions = parser.get_format_instructions()

        # Create the global metadata generation task
        global_metadata_task = Task(
            description=f"""Create global metadata for a Hindi documentary story series that will be split into multiple shorts:

            {story_content}

            {trending_keywords_text}

            Generate global metadata that includes:

            1. **Base Title**: Create a compelling base title in English that:
               - Is 40-50 characters or less (to allow for "Part X | " prefix)
               - Works with part numbering (e.g., "Part 1 | [your-title]", "Part 2 | [your-title]")
               - Incorporates trending keywords naturally
               - Creates curiosity and encourages clicks
               - Appeals to Hindi/Indian audiences
               - Represents the overall story theme

            2. **Base Hashtags**: Create 15-20 base hashtags in English that:
               - Are relevant to the overall story theme
               - Include trending keywords naturally
               - Will be used across all shorts in the series
               - Mix broad and specific terms
               - Include story-specific and general documentary/educational tags
               - Use English hashtags for maximum reach

            3. **Additional Metadata**: Include:
               - Target audience description in English
               - Content category
               - Total number of shorts (estimate based on story length)

            Requirements:
            - Incorporate trending keywords naturally (don't force them)
            - Create consistent branding across the series
            - Ensure base title works with part numbering
            - Focus on hashtags that will work for all parts of the story""",
            agent=global_metadata_agent,
            expected_output=format_instructions
        )

        # Execute the task
        crew = Crew(
            agents=[global_metadata_agent],
            tasks=[global_metadata_task],
            process=Process.sequential,
            verbose=self.verbose
        )

        try:
            result = crew.kickoff()

            # Extract the result
            if hasattr(result, 'raw'):
                metadata_text = result.raw
            else:
                metadata_text = str(result)

            # Parse the result using the Pydantic parser
            global_metadata = GlobalMetadataParser.parse_output(parser, metadata_text)

            if global_metadata is None:
                logger.error("Could not parse global metadata result")
                raise ValueError("Failed to parse global metadata from agent")

            # Add trending keywords used to the metadata
            if trending_keywords:
                global_metadata.trending_keywords_used = [tk.keyword for tk in trending_keywords]

            logger.info("Global metadata generation completed successfully")
            return global_metadata

        except Exception as e:
            logger.error(f"Error generating global metadata: {str(e)}")
            raise


class ShortMetadataAgent:
    """
    Agent for generating short-specific metadata that supplements global metadata.

    This agent creates additional hashtags and custom descriptions for individual shorts
    using pre-generated keywords and the specific content of each short.
    """

    def __init__(self, verbose: bool = False, model: str = "gpt-4o-mini", provider: str = "openai"):
        """
        Initialize the ShortMetadataAgent.

        Args:
            verbose (bool): Enable verbose output from CrewAI agents
            model (str): Model name to use for LLM
            provider (str): Provider to use for LLM
        """
        self.verbose = verbose
        self.model = model
        self.provider = provider

    def generate_short_metadata(self, short: Short, keywords: List[str], global_metadata: GlobalMetadata) -> ShortSpecificMetadata:
        """
        Generate short-specific metadata for an individual short.

        Args:
            short (Short): Individual short object
            keywords (List[str]): Pre-generated keywords for the story
            global_metadata (GlobalMetadata): Global metadata for context

        Returns:
            ShortSpecificMetadata: Short-specific metadata including additional hashtags and description
        """
        logger.info(f"Generating short-specific metadata for short {short.short_number}: {short.title}")

        # Generate short-specific metadata
        short_metadata = self._generate_short_specific_metadata(short, keywords, global_metadata)

        logger.info(f"Short-specific metadata generation completed for short {short.short_number}")
        return short_metadata

    def _generate_short_specific_metadata(self,
                                          short: Short,
                                          keywords: List[str],
                                          global_metadata: GlobalMetadata) -> ShortSpecificMetadata:
        """
        Generate short-specific metadata using keywords and global context.

        Args:
            short (Short): Individual short object
            keywords (List[str]): Pre-generated keywords for the story
            global_metadata (GlobalMetadata): Global metadata for context

        Returns:
            ShortSpecificMetadata: Short-specific metadata including additional hashtags and description
        """
        # Prepare short content
        short_content = f"Short {short.short_number}: {short.title}\n"
        short_content += f"Duration: {short.estimated_duration_seconds} seconds\n"
        short_content += f"Content: {short.narration}\n"
        short_content += f"Source Scenes: {short.source_scenes}\n"

        # Prepare keywords text
        keywords_text = "AVAILABLE KEYWORDS TO USE:\n"
        for keyword in keywords:
            keywords_text += f"- {keyword}\n"

        # Prepare global context
        global_context = f"BASE TITLE: {global_metadata.base_title}\n"
        global_context += f"BASE HASHTAGS: {', '.join(global_metadata.base_hashtags)}\n"
        global_context += f"TARGET AUDIENCE: {global_metadata.target_audience}\n"

        # Create the short metadata generation agent
        short_metadata_agent = create_rate_limited_agent(
            role="Short-Form Content Specialist",
            goal="Create additional hashtags and compelling descriptions for individual shorts that complement the global metadata",
            backstory="""You are an expert in short-form video content optimization with deep understanding
            of how to make each part of a series stand out while maintaining consistency with the overall brand.
            You specialize in creating specific hashtags and descriptions that highlight the unique aspects of
            each short while leveraging the pre-generated keywords effectively.""",
            model=self.model,
            provider=self.provider,
            verbose=self.verbose,
            allow_delegation=False,
        )

        # Create parser for structured output
        parser = ShortSpecificMetadataParser.create()
        format_instructions = parser.get_format_instructions()

        # Create the short metadata generation task
        short_metadata_task = Task(
            description=f"""Create short-specific metadata for this individual short that will supplement the global metadata:

            {short_content}

            GLOBAL CONTEXT:
            {global_context}

            {keywords_text}

            Generate short-specific metadata that includes:

            1. **Additional Hashtags**: Create 5-10 additional hashtags in English that:
               - Are specific to this short's content and themes
               - Complement (don't duplicate) the base hashtags
               - Use the provided keywords strategically
               - Highlight unique aspects of this particular short
               - Include specific terms related to the content of this short
               - Use English hashtags for maximum reach

            2. **Custom Description**: Create a compelling description in English that:
               - Is 100-150 words long
               - Uses the provided keywords naturally
               - Highlights the specific content and value of this short
               - Creates curiosity and encourages viewing
               - Mentions this is part of a series (e.g., "Part {short.short_number} of our series")
               - Includes a call-to-action for engagement
               - Appeals to the target audience

            Requirements:
            - Focus on what makes THIS short unique and valuable
            - Use the provided keywords strategically in the description
            - Create hashtags that are specific to this short's content
            - Ensure the description works well for short-form video platforms
            - Maintain consistency with the global brand and tone""",
            agent=short_metadata_agent,
            expected_output=format_instructions
        )

        # Execute the task
        crew = Crew(
            agents=[short_metadata_agent],
            tasks=[short_metadata_task],
            process=Process.sequential,
            verbose=self.verbose
        )

        try:
            result = crew.kickoff()

            # Extract the result
            if hasattr(result, 'raw'):
                metadata_text = result.raw
            else:
                metadata_text = str(result)

            # Parse the result using the Pydantic parser
            short_metadata = ShortSpecificMetadataParser.parse_output(parser, metadata_text)

            if short_metadata is None:
                logger.error(f"Could not parse short metadata result for short {short.short_number}")
                raise ValueError(f"Failed to parse short metadata from agent for short {short.short_number}")

            # Set the short number and duration
            short_metadata.short_number = short.short_number
            short_metadata.estimated_duration = f"{short.estimated_duration_seconds} seconds"

            logger.info(f"Short-specific metadata generation completed for short {short.short_number}")
            return short_metadata

        except Exception as e:
            logger.error(f"Error generating short metadata for short {short.short_number}: {str(e)}")
            raise


class TwoTierVideoMetadataAgent:
    """
    Main agent that orchestrates the two-tier metadata generation system.

    This agent coordinates both GlobalMetadataAgent and ShortMetadataAgent to create
    a complete two-tier metadata system for shorts-based content.
    """

    def __init__(self, verbose: bool = False, model: str = "gpt-4o-mini", provider: str = "openai"):
        """
        Initialize the TwoTierVideoMetadataAgent.

        Args:
            verbose (bool): Enable verbose output from CrewAI agents
            model (str): Model name to use for LLM
            provider (str): Provider to use for LLM
        """
        self.verbose = verbose
        self.model = model
        self.provider = provider

        # Initialize sub-agents
        self.global_agent = GlobalMetadataAgent(verbose=verbose, model=model, provider=provider)
        self.short_agent = ShortMetadataAgent(verbose=verbose, model=model, provider=provider)

    def generate_two_tier_metadata(self, story: Story, shorts: List[Short], story_dir: str) -> tuple[GlobalMetadata, List[ShortSpecificMetadata]]:
        """
        Generate complete two-tier metadata for a story and its shorts.

        Args:
            story (Story): Complete story object
            shorts (List[Short]): List of shorts extracted from the story
            story_dir (str): Directory path where metadata files will be saved

        Returns:
            tuple[GlobalMetadata, List[ShortSpecificMetadata]]: Global metadata and list of short-specific metadata
        """
        logger.info(f"Starting two-tier metadata generation for story: {story.title}")

        # Step 1: Generate keywords from the complete story
        keywords = self._generate_keywords_from_story(story)

        # Step 2: Generate global metadata
        global_metadata = self.global_agent.generate_global_metadata(story, keywords)

        # Update total_shorts in global metadata
        global_metadata.total_shorts = len(shorts)

        # Step 3: Save global metadata to story root directory
        self._save_global_metadata(global_metadata, story_dir)

        # Step 4: Generate short-specific metadata for each short
        short_metadata_list = []
        for short in shorts:
            short_metadata = self.short_agent.generate_short_metadata(short, keywords, global_metadata)
            short_metadata_list.append(short_metadata)

            # Save short-specific metadata to individual short directory
            self._save_short_metadata(short_metadata, story_dir, short.short_number)

        logger.info("Two-tier metadata generation completed successfully")
        return global_metadata, short_metadata_list

    def _generate_keywords_from_story(self, story: Story) -> List[str]:
        """
        Generate keywords from story content.

        Args:
            story (Story): Complete story object

        Returns:
            List[str]: List of generated keywords
        """
        # Prepare story content
        story_content = f"Title: {story.title}\n\n"
        for scene in story.scenes:
            story_content += f"Scene {scene.scene_number}: {scene.narration}\n\n"

        # Create the keyword generation agent
        keyword_agent = create_rate_limited_agent(
            role="SEO Keyword Specialist",
            goal="Generate comprehensive and relevant keywords for video content optimization",
            backstory="""You are an expert in SEO and video content optimization with deep knowledge
            of trending topics, search patterns, and keyword strategies. You specialize in analyzing
            Hindi documentary content and generating English keywords that will help the content reach
            the right audience through search and discovery algorithms.""",
            model=self.model,
            provider=self.provider,
            verbose=self.verbose,
            allow_delegation=False,
        )

        # Create the keyword generation task
        keyword_task = Task(
            description=f"""Analyze the following Hindi story content and generate exactly 100 relevant English keywords
            that could be used for video SEO and social media optimization:

            STORY CONTENT:
            {story_content}

            Generate 100 English keywords that include:
            1. Main topic keywords (10-15 keywords)
            2. Character and people-related keywords (10-15 keywords)
            3. Location and place keywords (10-15 keywords)
            4. Event and action keywords (15-20 keywords)
            5. Emotional and descriptive keywords (10-15 keywords)
            6. Documentary and storytelling keywords (10-15 keywords)
            7. Indian culture and context keywords (10-15 keywords)
            8. Long-tail keyword phrases (10-15 keywords)

            Requirements:
            - ALL keywords must be in English and relevant to the story content
            - Include both broad and specific terms
            - Mix of single words and short phrases (2-4 words)
            - Consider trending topics and popular search terms
            - Include character names and key people mentioned
            - Focus on terms that would help the video be discovered by interested viewers
            - Consider SEO best practices for video content
            - Use English terms that would be searched by Hindi content viewers

            Return the keywords as a simple JSON array of strings, exactly 100 English keywords total.
            Example format: ["keyword1", "keyword2", "keyword3", ...]""",
            agent=keyword_agent,
            expected_output="A JSON array containing exactly 100 relevant keywords as strings"
        )

        # Execute the task
        crew = Crew(
            agents=[keyword_agent],
            tasks=[keyword_task],
            process=Process.sequential,
            verbose=self.verbose
        )

        try:
            result = crew.kickoff()

            # Extract keywords from the result
            if hasattr(result, 'raw'):
                keywords_text = result.raw
            else:
                keywords_text = str(result)

            # Parse JSON array of keywords
            try:
                json_match = re.search(r'\[[\s\S]*\]', keywords_text)
                if json_match:
                    keywords_json = json_match.group(0)
                    keywords = json.loads(keywords_json)

                    if isinstance(keywords, list) and len(keywords) > 0:
                        logger.info(f"Generated {len(keywords)} keywords from story content")
                        return keywords
                    else:
                        logger.warning("No valid keywords found in result")
                        return []
                else:
                    logger.warning("Could not find JSON array in keywords result")
                    return []

            except json.JSONDecodeError as e:
                logger.error(f"Error parsing keywords JSON: {str(e)}")
                return []

        except Exception as e:
            logger.error(f"Error generating keywords from story: {str(e)}")
            return []

    def _save_global_metadata(self, global_metadata: GlobalMetadata, story_dir: str):
        """
        Save global metadata to the story root directory.

        Args:
            global_metadata (GlobalMetadata): Global metadata to save
            story_dir (str): Path to the story root directory
        """

        metadata_path = os.path.join(story_dir, "video_metadata.json")
        try:
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(global_metadata.model_dump(), f, ensure_ascii=False, indent=2)

            logger.info(f"Global metadata saved to {metadata_path}")

        except Exception as e:
            logger.error(f"Error saving global metadata to {metadata_path}: {str(e)}")
            raise

    def _save_short_metadata(self, short_metadata: ShortSpecificMetadata, story_dir: str, short_number: int):
        """
        Save short-specific metadata to the individual short directory.

        Args:
            short_metadata (ShortSpecificMetadata): Short-specific metadata to save
            story_dir (str): Path to the story root directory
            short_number (int): Number of the short
        """

        short_dir = os.path.join(story_dir, f"short-{short_number}")
        metadata_path = os.path.join(short_dir, "metadata.json")

        # Ensure the short directory exists
        os.makedirs(short_dir, exist_ok=True)

        try:
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(short_metadata.model_dump(), f, ensure_ascii=False, indent=2)

            logger.info(f"Short-specific metadata saved to {metadata_path}")

        except Exception as e:
            logger.error(f"Error saving short metadata to {metadata_path}: {str(e)}")
            raise
