"""
Visual Planning Agent
--------------------
Generates comprehensive visual plans for entire short segments to improve context and consistency.
"""

import os
import json
import logging
from typing import Optional

from crewai import Task, Crew, Process

from utils.parsers import VisualPlanParser
from models.schema import Short, VisualPlan
from utils.agent_factory import create_rate_limited_agent
from utils.shorts_utils import get_shorts_directories, load_short_from_directory

logger = logging.getLogger(__name__)


class VisualPlanningAgent:
    """
    Agent for generating comprehensive visual plans for entire short segments.
    
    This agent analyzes complete short segments to create cohesive visual prompts
    with better story context and narrative consistency across all segments.
    """

    def __init__(self, verbose: bool = False, model: str = "gpt-4o-mini", provider: str = "openai"):
        """
        Initialize the Visual Planning Agent.

        Args:
            verbose (bool): Whether to enable verbose logging
            model (str): The model to use for the agent
            provider (str): The provider to use for the agent
        """
        self.verbose = verbose
        self.model = model
        self.provider = provider

    def create_visual_plan_from_short(self, short: Short) -> VisualPlan:
        """
        Create a comprehensive visual plan directly from a short's narration.
        This method divides the short narration into 3-10 second segments for optimal video generation.

        Args:
            short (Short): The short containing the complete narration

        Returns:
            VisualPlan: Complete visual planning data for the short
        """
        logger.info(f"Creating visual plan for short {short.short_number} from complete narration")

        # Create a parser for the VisualPlan model
        parser = VisualPlanParser.create()
        format_instructions = parser.get_format_instructions()

        # Create the visual planning agent
        visual_planning_agent = create_rate_limited_agent(
            role="Visual Story Planner",
            goal="Create comprehensive visual plans for Hindi story segments that ensure narrative consistency and optimal video generation with bytedance/seedance-1-lite",
            backstory="""You are an expert visual storyteller and AI video generation specialist with deep understanding of
            the bytedance/seedance-1-lite model capabilities. You excel at analyzing complete story contexts to create
            cohesive visual narratives that maintain consistency across multiple video segments. You understand how to
            create visual prompts that work perfectly with text-to-video AI models while ensuring each segment flows
            naturally into the next, creating a compelling visual story experience. You are skilled at dividing narration
            into optimal segments for video generation while maintaining narrative coherence.""",
            model=self.model,
            provider=self.provider,
            verbose=self.verbose
        )

        # Create the visual planning task
        planning_task = Task(
            description=f"""
            Create a comprehensive visual plan for the following Hindi short story that will be used with
            the bytedance/seedance-1-lite video generation model:

            SHORT CONTEXT:
            Title: {short.title}
            Complete Narration: {short.narration}

            Your task is to:

            1. **Divide Narration into Optimal Segments**: Break down the complete narration into 3-10 second segments
               that work perfectly for video generation. Each segment should:
               - Contain a complete thought or narrative beat
               - Be suitable for a single video clip
               - Flow naturally into the next segment
               - Target 3-10 seconds of narration content

            2. **Analyze Complete Context**: Understand the full story arc, key themes, and narrative flow.

            3. **Create Cohesive Visual Narrative**: Design visual scenes that work together to tell a compelling story,
               ensuring smooth transitions and consistent visual elements across segments.

            4. **Optimize for bytedance/seedance-1-lite**: Create visual prompts that work perfectly with this specific
               text-to-video model, considering its strengths in:
               - Realistic human expressions and movements
               - Dynamic camera movements and angles
               - Professional lighting and composition
               - Vertical video format (9:16 aspect ratio)

            5. **Handle Text and Logo Issues**: To address video generation model limitations with text and logos:
               - Use start_with_image: true when you need specific text, logos, or complex environments
               - Use end_with_image: true when the segment should end with a specific visual
               - Use start_with_prev_frame: true to continue from the previous segment's end frame
               - Provide detailed start_image_prompt and end_image_prompt when needed

            6. **Set Fixed Video Duration**: All segments will use 5-second video clips that will be speed-adjusted
               to match the narration timing, so focus on content division rather than precise timing.

            7. **Create Engaging Visuals**: Design scenes that are visually compelling and complement the Hindi
               narration, enhancing the storytelling experience.

            For each segment, provide:
            - segment_number: Sequential number starting from 1
            - narration: The exact narration text for this segment
            - visual_prompt: A detailed, engaging visual description (2-3 sentences) optimized for bytedance/seedance-1-lite
            - start_with_prev_frame: true if this segment should continue from the previous segment's end frame
            - start_with_image: true if you need a specific start image (for text, logos, complex scenes)
            - end_with_image: true if you need a specific end image
            - start_image_prompt: Detailed prompt for start image (if start_with_image is true)
            - end_image_prompt: Detailed prompt for end image (if end_with_image is true)

            IMPORTANT: Focus on creating 3-10 segments total, each representing 3-10 seconds of narration content.
            Prioritize narrative coherence and visual storytelling.
            
            NOTE: If end_with_image is true then we required start_with_image or start_with_prev_frame to be true. The video generation model does not support
            only end image without start image. But it can work with start image alone. Also either start_with_image or start_with_prev_frame can be true, not both.
            And for the 1st numbered segment, start_with_prev_frame should be false.
            """,
            agent=visual_planning_agent,
            expected_output=format_instructions
        )

        # Create and run the crew
        crew = Crew(
            process=Process.sequential,
            tasks=[planning_task],
            agents=[visual_planning_agent],
            verbose=self.verbose,
        )

        crew_output = crew.kickoff()
        result = crew_output.raw

        # Parse the result using the Pydantic parser
        visual_plan = VisualPlanParser.parse_output(parser, result)

        # If parsing fails, log the error and exit
        if visual_plan is None:
            logger.error("Could not parse VisualPlanner result for short %d, Raw output: %s", short.short_number, result)
            raise ValueError(f"Failed to parse visual plan from VisualPlanner Agent for short {short.short_number}")

        logger.info(f"Successfully created visual plan for short {short.short_number} with {len(visual_plan.segments)} segments")

        # Log details about each segment
        for segment in visual_plan.segments:
            logger.info(f"Segment {segment.segment_number}: "
                       f"start_with_prev: {segment.start_with_prev_frame}, "
                       f"start_with_image: {segment.start_with_image}, "
                       f"end_with_image: {segment.end_with_image}")

        return visual_plan

    def save_visual_plan(self, visual_plan: VisualPlan, short_dir: str) -> None:
        """
        Save visual plan to JSON file in the short directory.

        Args:
            visual_plan (VisualPlan): The visual plan to save
            short_dir (str): Path to the short directory
        """
        visuals_json_path = os.path.join(short_dir, "visuals.json")
        
        try:
            with open(visuals_json_path, 'w', encoding='utf-8') as f:
                json.dump(visual_plan.model_dump(), f, ensure_ascii=False, indent=2)
            
            logger.info(f"Visual plan saved to: {visuals_json_path}")
            
        except Exception as e:
            logger.error(f"Error saving visual plan to {visuals_json_path}: {str(e)}")
            raise

    def load_visual_plan(self, short_dir: str) -> Optional[VisualPlan]:
        """
        Load visual plan from JSON file in the short directory.

        Args:
            short_dir (str): Path to the short directory

        Returns:
            Optional[VisualPlan]: The loaded visual plan, or None if not found/invalid
        """
        visuals_json_path = os.path.join(short_dir, "visuals.json")
        
        if not os.path.exists(visuals_json_path):
            return None
        
        try:
            with open(visuals_json_path, 'r', encoding='utf-8') as f:
                visual_plan_data = json.load(f)
                return VisualPlan(**visual_plan_data)
        except Exception as e:
            logger.error(f"Error loading visual plan from {visuals_json_path}: {str(e)}")
            return None

    def process_short(self, short_dir: str) -> bool:
        """
        Process a single short directory to create visual plan.

        Args:
            short_dir (str): Path to the short directory

        Returns:
            bool: True if successful, False otherwise
        """
        short_name = os.path.basename(short_dir)
        
        # Check if visual plan already exists
        if self.load_visual_plan(short_dir) is not None:
            logger.info(f"Visual plan already exists for {short_name}, skipping")
            return True

        try:
            # Load the short data
            short = load_short_from_directory(short_dir)
            if not short:
                logger.error(f"Could not load short data from {short_dir}")
                return False

            # Create visual plan directly from short narration
            visual_plan = self.create_visual_plan_from_short(short)

            # Save visual plan
            self.save_visual_plan(visual_plan, short_dir)

            logger.info(f"Successfully created visual plan for {short_name}")
            return True

        except Exception as e:
            logger.error(f"Error processing visual plan for {short_name}: {str(e)}")
            return False

    def process_all_shorts(self, story_dir: str) -> bool:
        """
        Process all shorts in the story directory to create visual plans.

        Args:
            story_dir (str): Path to the story directory

        Returns:
            bool: True if all shorts processed successfully, False otherwise
        """
        short_dirs = get_shorts_directories(story_dir)
        if not short_dirs:
            logger.warning(f"No shorts directories found in {story_dir}")
            return False

        success_count = 0
        total_shorts = len(short_dirs)

        for short_dir in short_dirs:
            if self.process_short(short_dir):
                success_count += 1

        logger.info(f"Visual planning completed: {success_count}/{total_shorts} shorts processed successfully")
        return success_count == total_shorts
